import { GetRestaurantsReviewsV2ResponseDto } from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import z from 'zod';
import Config from '../shared/config.js';
import { makeMalouRequest } from '../shared/make-request.js';

export const registerGetReviewsTool = (server: McpServer) => {
    server.tool(
        'list_restaurants',
        'Get restaurants from Malou API',
        {
            text: z.string().optional().describe('Search text to filter restaurants'),
            fields: z.array(z.string()).optional().describe('Array of fields to fetch, none equals name & address by default'),
            limit: z.string().transform(Number).optional().describe('Number of restaurants per page (1-any)'),
            offset: z.string().transform(Number).optional().describe('Offset (0-based)'),
            active: z
                .string()
                .transform((value) => value === 'true')
                .optional()
                .describe('Filter by active status'),
        },
        async (params) => {
            try {
                // Build query parameters
                const queryParams = new URLSearchParams();

                // Required parameter
                params.restaurant_ids.forEach((id) => queryParams.append('restaurant_ids', id));

                // Optional parameters
                if (params.page_number !== undefined) queryParams.set('page_number', params.page_number.toString());
                if (params.page_size !== undefined) queryParams.set('page_size', params.page_size.toString());
                if (params.text) queryParams.set('text', params.text);
                if (params.ratings) params.ratings.forEach((rating) => queryParams.append('ratings', rating.toString()));
                if (params.start_date) queryParams.set('start_date', params.start_date);
                if (params.end_date) queryParams.set('end_date', params.end_date);
                if (params.platforms) params.platforms.forEach((platform) => queryParams.append('platforms', platform));
                if (params.answered !== undefined) queryParams.set('answered', params.answered.toString());
                if (params.not_answered !== undefined) queryParams.set('not_answered', params.not_answered.toString());
                if (params.pending !== undefined) queryParams.set('pending', params.pending.toString());
                if (params.archived !== undefined) queryParams.set('archived', params.archived.toString());
                if (params.unarchived !== undefined) queryParams.set('unarchived', params.unarchived.toString());
                if (params.with_text !== undefined) queryParams.set('with_text', params.with_text.toString());
                if (params.without_text !== undefined) queryParams.set('without_text', params.without_text.toString());
                if (params.show_private !== undefined) queryParams.set('show_private', params.show_private.toString());
                if (params.answerable !== undefined) queryParams.set('answerable', params.answerable.toString());

                const reviewsUrl = `${Config.MALOU_API_BASE}/reviews/v2?${queryParams.toString()}`;
                const reviewsData: ApiResultV2<GetRestaurantsReviewsV2ResponseDto> | null =
                    await makeMalouRequest<ApiResultV2<GetRestaurantsReviewsV2ResponseDto>>(reviewsUrl);

                if (!reviewsData) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: 'Failed to retrieve reviews data from Malou API',
                            },
                        ],
                    };
                }

                const { reviews, pagination } = reviewsData.data;

                if (reviews.length === 0) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `No reviews found for the specified criteria. Total: ${pagination}`,
                            },
                        ],
                    };
                }

                // Format reviews for display
                const formattedReviews = reviews.map((review) => {
                    const parts = [
                        `Review ID: ${review._id}`,
                        `Restaurant ID: ${review.restaurantId}`,
                        `Rating: ${review.rating || 'No rating'}`,
                        `Platform: ${review.key || 'Unknown'}`,
                        `Created: ${review.socialCreatedAt ? new Date(review.socialCreatedAt).toLocaleDateString() : 'Unknown'}`,
                        `Status: ${review.comments?.length > 0 ? 'Answered' : 'Not answered'}`,
                        `Archived: ${review.archived ? 'Yes' : 'No'}`,
                    ];

                    if (review.text) {
                        parts.push(`Text: ${review.text.substring(0, 200)}${review.text.length > 200 ? '...' : ''}`);
                    }

                    return parts.join('\n') + '\n---';
                });

                const reviewsText = [`Reviews for restaurants [${params.restaurant_ids.join(', ')}]:`, ``, ``, ...formattedReviews].join(
                    '\n'
                );

                return {
                    content: [
                        {
                            type: 'text',
                            text: reviewsText,
                        },
                    ],
                };
            } catch (error) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error fetching reviews: ${error instanceof Error ? error.message : 'Unknown error'}`,
                        },
                    ],
                };
            }
        }
    );
};
